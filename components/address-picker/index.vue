<template>
  <u-popup v-model="showAddressPicker" mode="bottom" @close="addressCanel">
    <view class="popup-view">
      <view class="picker-btn">
        <view class="left" @click="pickerCancel">取消</view>
        <view class="middle" v-if="titleShow">监管区域</view>
        <view class="right" @click="submitConfirm">确定</view>
      </view>
      <picker-view
        :indicator-style="indicatorStyle"
        :value="multiIndex"
        @change="pickerChange"
      >
        <picker-view-column>
          <view class="item" v-for="(item, index) in area" :key="index"
            >{{ item.text }}
          </view>
        </picker-view-column>
        <picker-view-column v-if="area[multiIndex[0]]">
          <view
            class="item"
            v-for="(item, index) in area[multiIndex[0]].children"
            :key="index"
          >
            {{ item.text }}</view
          >
        </picker-view-column>
        <!-- <picker-view-column v-if="area[multiIndex[0]]">
          <view
            class="item"
            v-for="(item, index) in area[multiIndex[0]].children[multiIndex[1]]
              .children"
            :key="index"
          >
            {{ item.text }}</view
          >
        </picker-view-column> -->
      </picker-view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    pickerAreaShow: {
      type: Boolean,
      default: false,
    },
    titleShow: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      multiIndex: [0, 0, 0],
      area: [],
      allAddress: [],
      provincialLevel: [],
      showAddressPicker: false,
      indicatorStyle: `height: ${Math.round(
        uni.getSystemInfoSync().screenWidth / (750 / 100)
      )}px;`,
    };
  },
  computed: {},
  watch: {
    pickerAreaShow: {
      handler(newValue, oldValue) {
        this.showAddressPicker = newValue;
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    this.getList();
  },
  methods: {
    async getList() {
      uni.request({
        url: "https://nmb-new.obs.cn-north-4.myhuaweicloud.com/2025/03/provincialLevel.js",
        success: (res) => {
          this.area = res.data;
        },
      });
    },
    pickerChange(e) {
      this.multiIndex = e.detail.value;
    },
    addressCanel() {
      this.$emit("addressCanel");
    },
    // 取消picker
    pickerCancel() {
      this.showAddressPicker = false;
      this.multiIndex = [0, 0, 0];
    },
    submitConfirm() {
      const arrIndex = this.multiIndex;
      const provinceValue = this.area[arrIndex[0]].id;
      const cityValue = this.area[arrIndex[0]].children[arrIndex[1]].id;
      /* const areaValue =
        this.area[arrIndex[0]].children[arrIndex[1]].children[arrIndex[2]]
          .id; */
      const province = this.area[arrIndex[0]].text;
      const city = this.area[arrIndex[0]].children[arrIndex[1]].text;
      /* const area =
        this.area[arrIndex[0]].children[arrIndex[1]].children[arrIndex[2]]
          .text; */
      const form = {};
      form.areaName = province + "-" + city;
      form.areaValue =  provinceValue + "," + cityValue;
      this.$emit("submitAddress", form);
      this.showAddressPicker = false;
    },
  },
};
</script>

<style lang="scss" scoped>
uni-picker-view {
  display: block;
}

uni-picker-view .uni-picker-view-wrapper {
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  height: 100%;
  background-color: white;
}

uni-picker-view[hidden] {
  display: none;
}

picker-view {
  width: 100%;
  // height: 600upx;
  height: 600rpx;
  margin-top: 20upx;
}

.item {
  line-height: 100upx;
  text-align: center;
}

.popup-view {
  .picker-btn {
    display: flex;
    justify-content: space-between;
    padding: 30rpx 40rpx;

    .left {
      color: #999;
      font-size: 28rpx;
      font-family: PingFang SC-Medium;
    }

    .middle {
      font-size: 30rpx;
      color: #222;
      font-family: PingFang SC-Heavy;
    }

    .right {
      color: #40CA8F;
      font-size: 30rpx;
      font-family: PingFang SC-Medium;
    }
  }
}
</style>