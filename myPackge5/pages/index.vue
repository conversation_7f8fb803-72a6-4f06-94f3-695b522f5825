<template>
    <view class="container">
        <CustomNavbar title="活畜管理" :titleColor="'##333333'" />
        <view class="content" :style="{ paddingTop: navbarTotalHeight + 'px' }">
            <view class="module-item" @click="goURL('/myPackge5/pages/farm/index')">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/yanghzichang.png"
                    mode="widthFix"
                />
            </view>
            <view class="module-item" @click="goURL('/myPackge5/pages/cow/index')">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/kuru.png"
                    mode="widthFix"
                />
            </view>
            <view class="module-item" @click="goURL('/myPackge5/pages/cow/index')">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/zaiyang.png"
                    mode="widthFix"
                />
            </view>
            <view class="module-item" @click="goURL('/myPackge5/pages/cow/index')">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/chuku.png"
                    mode="widthFix"
                />
            </view>
            <view class="module-item" @click="goURL('/myPackge5/pages/cow/index')">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/fanzhi.png"
                    mode="widthFix"
                />
            </view>
        </view>
    </view>
</template>

<script>

import CustomNavbar from './components/CustomNavbar.vue'

export default {
    components: {
        CustomNavbar,
    },
    data() {
        return {
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            systemInfo: uni.getSystemInfoSync(),
        }
    },
    onLoad() {

    },
    onUnload() {
    },
    onShow() { },
    computed: {
        navbarTotalHeight() {
            const statusBarHeight = this.systemInfo.statusBarHeight || 0;
            const navbarHeight = 44;
            return statusBarHeight + navbarHeight;
        }
    },
    methods: {
        goURL(url){
            uni.navigateTo({
                url: url
            })
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
}

.content {
    padding: 30rpx;
    background-color: #F7F8F7;
}
.module-item {
    margin-bottom: 24rpx;
    &:last-child {
        margin-bottom: 0;
    }
}

.module-item:first-child {
    margin-top: 30rpx;
}

.module-image {
    width: 100%;
    display: block;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
</style>
