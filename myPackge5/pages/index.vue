<template>
    <view class="container">
        <CustomNavbar title="活畜管理" :titleColor="'##333333'" />

        <!-- 页面内容区域 -->
        <view class="content">
            <!-- 羊只存栏 -->
            <view class="module-item">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/yanghzichang.png"
                    mode="widthFix"
                />
            </view>

            <!-- 库存 -->
            <view class="module-item">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/kuru.png"
                    mode="widthFix"
                />
            </view>

            <!-- 在养 -->
            <view class="module-item">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/zaiyang.png"
                    mode="widthFix"
                />
            </view>

            <!-- 出库 -->
            <view class="module-item">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/chuku.png"
                    mode="widthFix"
                />
            </view>

            <!-- 繁殖 -->
            <view class="module-item">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/fanzhi.png"
                    mode="widthFix"
                />
            </view>
        </view>
    </view>
</template>

<script>

import CustomNavbar from './components/CustomNavbar.vue'

export default {
    components: {
        CustomNavbar,
    },
    data() {
        return {
            isIphonex: getApp().globalData.systemInfo.isIphonex,
        }
    },
    onLoad() {

    },
    onUnload() {
    },
    onShow() { },
    computed: {

    },
    methods: {}
}
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background-color: #f5f5f5;
}

.content {
    padding: 20rpx;
    padding-top: 30rpx;
}

.module-item {
    margin-bottom: 20rpx;

    &:last-child {
        margin-bottom: 0;
    }
}

.module-image {
    width: 100%;
    display: block;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
</style>
