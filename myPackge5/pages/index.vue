<template>
    <view>
        <CustomNavbar  title="活畜管理" :titleColor="'##333333'" />
    </view>
</template>

<script>

import CustomNavbar from './components/CustomNavbar.vue'

export default {
    components: {
        CustomNavbar,
    },
    data() {
        return {
            isIphonex: getApp().globalData.systemInfo.isIphonex,

        }
    },
    onLoad() {

    },
    onUnload() {
    },
    onShow() { },
    computed: {

    },
    methods: {}
}
</script>

<style lang="scss" scoped></style>
