<template>
    <view class="container">
        <CustomNavbar title="活畜管理" :titleColor="'##333333'" />

        <!-- 页面内容区域 -->
        <view class="content">
            <!-- 羊只存栏 -->
            <view class="module-item">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/yanghzichang.png"
                    mode="widthFix"
                />
            </view>

            <!-- 库存 -->
            <view class="module-item">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/kuru.png"
                    mode="widthFix"
                />
            </view>

            <!-- 在养 -->
            <view class="module-item">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/zaiyang.png"
                    mode="widthFix"
                />
            </view>

            <!-- 出库 -->
            <view class="module-item">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/chuku.png"
                    mode="widthFix"
                />
            </view>

            <!-- 繁殖 -->
            <view class="module-item">
                <image
                    class="module-image"
                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/fanzhi.png"
                    mode="widthFix"
                />
            </view>
        </view>
    </view>
</template>

<script>

import CustomNavbar from './components/CustomNavbar.vue'

export default {
    components: {
        CustomNavbar,
    },
    data() {
        return {
            isIphonex: getApp().globalData.systemInfo.isIphonex,
        }
    },
    onLoad() {

    },
    onUnload() {
    },
    onShow() { },
    computed: {

    },
    methods: {}
}
</script>

<style lang="scss" scoped></style>
