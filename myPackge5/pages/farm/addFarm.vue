<template>
    <view>
        <CustomNavbar title="新建养殖场" :titleColor="'##333333'" />
        <scroll-view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }" scroll-y
            :scroll-with-animation="true">
            <view class="container">
                <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" :label-style="labelStyle">
                    <u-form-item label="养殖场名称" required prop="pastureName">
                        <u-input v-model="form.pastureName" maxlength="10" placeholder="请输入养殖场名称，10个字以内"
                            :custom-style="customStyle" :placeholder-style="placeholderStyle" />
                    </u-form-item>
                    <u-form-item label="养殖场性质" required prop="pastureNature" right-icon="arrow-right">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="getNatureText(form.pastureNature)" placeholder="请选择养殖场性质" disabled
                            @click="showNatureSelect = true" />
                    </u-form-item>
                    <u-form-item label="养殖场位置" required prop="provinceName" right-icon="arrow-right">
                        <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                            :value="form.provinceName ? `${form.provinceName}-${form.cityName}-${form.countyName}` : ''"
                            placeholder="请选择养殖场位置" disabled @click="pickerAreaShow = true" />
                    </u-form-item>
                    <u-form-item label="详细地址" required prop="address">
                        <u-input v-model="form.address" placeholder="请输入详细地址" :custom-style="customStyle"
                            :placeholder-style="placeholderStyle" />
                    </u-form-item>
                </u-form>
                <view class="cultivate-section">
                    <view class="section-title">养殖方式：</view>
                    <view class="cultivate-options">
                        <view v-for="item in cultivateList" :key="item.value"
                            :class="['cultivate-item', { 'active': form.cultivateType === item.value }]"
                            @click="selectCultivateType(item.value)">
                            {{ item.label }}
                        </view>
                    </view>
                </view>
            </view>
            <view class="upload-section">
                <view class="section-title">
                    养殖场照片：
                    <img class="upload-icon" src="../../icon/photo.png" @click="uploadImage('farmImages', 3)"></img>
                </view>
                <view class="section-subtitle">最多不超过3张，单张图片小于2M</view>

                <view class="uploadImage" v-if="form.farmImages.length">
                    <view class="itemAlready" v-for="(item, index) in form.farmImages" :key="index">
                        <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
                        <view class="closeIcon" @click="deleteImage(index, 'farmImages')"></view>
                    </view>
                </view>
            </view>
        </scroll-view>

        <view class="bg-box">
            <view class="add-btn" @click="submitForm">提交</view>
        </view>

        <address-picker @submitAddress="submitAddress" :pickerAreaShow="pickerAreaShow"
            @addressCanel="pickerAreaShow = false" :titleShow="false" />

        <u-select confirm-color='#40CA8F' v-model="showNatureSelect" mode="single-column" :list="natureList"
            label-name="label" @confirm="selectNature" value-name="value" />
    </view>
</template>

<script>
import addressPicker from '@/components/address-picker-level3/index.vue'
import { pastureAdd } from '@/api/pages/livestock/farm'
import { getDicts } from "@/api/dict.js"
import { uploadFiles } from '@/api/obsUpload/index'
import CustomNavbar from '../components/CustomNavbar.vue'

export default {
    name: 'addFarm',

    components: {
        addressPicker,
        CustomNavbar
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            form: {
                pastureName: '',
                address: '',
                pastureNature: '',
                cultivateType: '',
                farmImages: [],
                provinceName: '',
                provinceId: '',
                cityName: '',
                cityId: '',
                countyName: '',
                countyId: ''
            },
            pickerAreaShow: false,
            showNatureSelect: false,
            errorType: ['message'],
            customStyle: { textAlign: 'right', fontSize: '26rpx' },
            labelStyle: { color: '#333', fontSize: '26rpx' },
            placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
            isSubmitting: false,
            natureList: [],
            cultivateList: [],
            rules: {
                pastureName: [{
                    required: true,
                    message: '请输入养殖场名称',
                    trigger: ['blur', 'change']
                }],
                provinceName: [{
                    required: true,
                    message: '请选择养殖场位置',
                    trigger: ['blur', 'change']
                }],
                address: [{
                    required: true,
                    message: '请输入详细地址',
                    trigger: ['blur', 'change']
                }],
                pastureNature: [{
                    required: true,
                    message: '请选择养殖场性质',
                    trigger: ['blur', 'change']
                }],
                cultivateType: [{
                    required: true,
                    message: '请选择养殖方式',
                    trigger: ['blur', 'change']
                }],
                farmImages: [{
                    required: true,
                    validator: (rule, value, callback) => {
                        value?.length > 0 ? callback() : callback(new Error('请上传养殖场照片'))
                    },
                    message: '请上传养殖场照片',
                    trigger: 'change'
                }]
            }
        }
    },

    computed: {
        navbarTotalHeight() {
            const statusBarHeight = this.systemInfo.statusBarHeight || 0;
            const navbarHeight = 64;
            return statusBarHeight + navbarHeight;
        },
        isFormValid() {
            const { pastureName, provinceName, address, pastureNature, cultivateType, farmImages } = this.form
            return pastureName && provinceName && address && pastureNature !== '' && cultivateType !== '' && farmImages.length > 0
        }
    },

    onLoad() {
        this.loadNatureDict();
        this.loadCultivateDict();
    },

    onReady() {
        this.$refs.uForm?.setRules?.(this.rules)
    },

    methods: {
        // 加载养殖场性质字典
        async loadNatureDict() {
            try {
                const res = await getDicts('pasture_nature');
                if (res && res.data) {
                    this.natureList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载字典失败:', error);
                this.$toast('加载字典失败');
            }
        },

        async loadCultivateDict() {
            try {
                const res = await getDicts('cultivate_type');
                if (res && res.data) {
                    this.cultivateList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载养殖方式字典失败:', error);
            }
        },

        getNatureText(value) {
            const nature = this.natureList.find(item => item.value === value)
            return nature ? nature.label : ''
        },

        submitAddress(val) {
            if (!val?.areaName || !val?.areaValue) return

            const areaName = val.areaName.split('-')
            const areaId = val.areaValue.split(',')

            this.form = {
                ...this.form,
                provinceName: areaName[0] || '',
                provinceId: areaId[0] || '',
                cityName: areaName[1] || '',
                cityId: areaId[1] || '',
                countyName: areaName[2] || '',
                countyId: areaId[2] || ''
            }

            this.pickerAreaShow = false
            this.resetField('provinceName')
        },

        // 选择养殖场性质
        selectNature(value) {
            if (!value?.length) return
            this.form.pastureNature = value[0].value
            this.showNatureSelect = false
            this.resetField('pastureNature')
        },

        selectCultivateType(value) {
            this.form.cultivateType = value
            this.resetField('cultivateType')
        },

        // 上传图片
        uploadImage(type, maxCount) {
            if (this.form[type].length >= maxCount) {
                this.$toast(`图片最多只能上传${maxCount}张`)
                return
            }
            const that = this
            uni.chooseImage({
                count: maxCount - this.form[type].length,
                sizeType: ['original', 'compressed'],
                sourceType: ['album', 'camera'],
                success: function (res) {
                    res.tempFilePaths.forEach(filePath => {
                        uploadFiles({
                            filePath: filePath,
                        }).then((data) => {
                            that.form[type].push(data)
                            that.resetField(type)
                        }).catch(error => {
                            console.error('上传失败:', error)
                            that.$toast('上传失败')
                        })
                    })
                },
                fail(e) {
                    console.error('选择图片失败:', e)
                },
            })
        },

        // 预览图片
        previewImage(url) {
            uni.previewImage({
                urls: [url],
            })
        },

        // 删除图片
        deleteImage(index, type) {
            this.form[type].splice(index, 1)
            this.resetField(type)
            this.$forceUpdate()
        },

        // 提交表单
        async submitForm() {
            console.log(this.form)
            if (this.isSubmitting) return
            if (!this.isFormValid) {
                return this.$toast('请填写完整信息')
            }

            try {
                this.isSubmitting = true
                const valid = await this.validateForm()
                if (!valid) return

                const res = await pastureAdd(this.form)
                if (res.code === 200) {
                    uni.$emit('updateFarmList')
                    this.$toast('添加成功')
                    uni.navigateBack({ delta: 1 })
                } else {
                    throw new Error(res.message || '提交失败')
                }
            } catch (error) {
                this.handleError(error, '提交失败')
            } finally {
                this.isSubmitting = false
            }
        },

        validateForm() {
            return new Promise(resolve => {
                this.$refs.uForm.validate(valid => resolve(valid))
            })
        },

        handleError(error, customMessage = '') {
            console.error(error)
            this.$toast(error.message || customMessage || '操作失败')
        },

        resetField(value) {
            if (!value) return
            this.$refs.uForm?.fields?.forEach(field => {
                if (field.prop === value) {
                    field.resetField()
                }
            })
        }
    },
}
</script>

<style lang="less" scoped>
.main {
    height: calc(100vh - 126rpx);
    background-color: #F7F8F7;
}

.container {
    margin: 25rpx;
    background: #fff;
    box-sizing: border-box;
    padding: 30rpx 32rpx 40rpx;
    border-radius: 30rpx;

    /deep/ .u-form-item {
        padding: 20rpx 20rpx !important;
    }

    .tips {
        font-size: 28rpx;
        color: #999;
    }
}

.bg-box {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 126rpx;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
}

.add-btn {
    width: 100%;
    height: 86rpx;
    background: linear-gradient(101deg, #19AF77 0%, #40CA8F 100%);
    border-radius: 50rpx;
    font-weight: 600;
    font-size: 34rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 86rpx;
}

.tips {
    font-size: 28rpx;
    color: #c0c3ca;
}

.common {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    color: #333;
    margin: 10rpx;
    text-align: right;
}

.cultivate-section {
    margin: 25rpx 0;
    background: #fff;
    border-radius: 30rpx;

    .section-title {
        font-size: 26rpx;
        color: #333;
        margin-bottom: 30rpx;
        font-weight: 500;
    }

    .cultivate-options {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .cultivate-item {
            padding: 16rpx 40rpx;
            border-radius: 50rpx;
            font-size: 26rpx;
            color: #666;
            background-color: #F5F5F5;
            border: 2rpx solid transparent;
            transition: all 0.3s;
            height: 60rpx;
            display: flex;
            align-items: center;
            justify-content: center;

            &.active {
                color: #1DB17A;
                background-color: #fff;
                border-color: #1DB17A;
            }
        }
    }
}

.upload-section {
    background: #fff;
    padding: 30rpx;
    border-radius: 30rpx;
    margin: 0 30rpx;

    .section-title {
        font-size: 26rpx;
        color: #333;
        margin-bottom: 10rpx;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .upload-icon {
            width: 40rpx;
            height: 40rpx;
        }
    }

    .section-subtitle {
        font-size: 26rpx;
        color: #999;
        margin-bottom: 30rpx;
        font-weight: 400;
    }

    .uploadImage {
        display: flex;
        flex-wrap: wrap;
        gap: 20rpx;

        .itemAlready {
            width: 140rpx;
            height: 140rpx;
            border-radius: 8rpx;
            position: relative;
            margin: 0 20rpx 10rpx 0rpx;

            image {
                width: 100%;
                height: 100%;
                border-radius: 8rpx;
            }

            video {
                width: 100%;
                height: 100%;
                border-radius: 8rpx;
            }


            .closeIcon {
                width: 32rpx;
                height: 32rpx;
                background-image: url('../../../static/modalImg/error.png');
                position: absolute;
                background-size: cover;
                top: -10rpx;
                right: -10rpx;
            }

            .close-file {
                top: 5rpx;
                right: -20rpx;
            }
        }

        /* .itemAlready {
            position: relative;
            width: 140rpx;
            height: 140rpx;
            border-radius: 16rpx;
            overflow: hidden;

            image {
                width: 100%;
                height: 100%;
                border-radius: 16rpx;
            }

            .closeIcon {
                position: absolute;
                top: -10rpx;
                right: -10rpx;
                width: 32rpx;
                height: 32rpx;
                background-image: url('@/static/modalImg/error.png');
                background-size: cover;
                z-index: 10;
            }
        } */
    }
}
</style>
