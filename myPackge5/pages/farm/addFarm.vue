<template>
    <view>
        <CustomNavbar title="新建养殖场" :titleColor="'##333333'" />
        <scroll-view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }" scroll-y
            :scroll-with-animation="true">
            <view class="container">
                <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto" :label-style="labelStyle">
                    <!-- 养殖场名称 -->
                    <u-form-item label="养殖场名称" required prop="pastureName">
                        <u-input v-model="form.pastureName" maxlength="10" placeholder="请输入养殖场名称，10个字以内" :custom-style="customStyle"
                            :placeholder-style="placeholderStyle" />
                    </u-form-item>
                     <!-- 养殖场性质 -->
                    <u-form-item label="养殖场性质" required prop="pastureNature" right-icon="arrow-right" style="text-align: right">
                        <text :class="form.pastureNature !== '' ? 'common' : 'tips'" @click="showNatureSelect = true">
                            {{ getNatureText(form.pastureNature) || '请选择养殖场性质' }}
                        </text>
                    </u-form-item>
                    <!-- 养殖场位置 -->
                    <u-form-item label="养殖场位置" required prop="provinceName" style="text-align: right" right-icon="arrow-right">
                        <text :class="form.provinceName ? 'common' : 'tips'" @click="pickerAreaShow = true">
                            {{ form.provinceName ? `${form.provinceName}-${form.cityName}-${form.countyName}` :
                            '请选择养殖场位置' }}
                        </text>
                    </u-form-item>
                    <!-- 详细地址 -->
                    <u-form-item label="详细地址" required prop="address">
                        <u-input v-model="form.address" placeholder="请输入详细" :custom-style="customStyle"
                            :placeholder-style="placeholderStyle" />
                    </u-form-item>
                </u-form>
            </view>
        </scroll-view>

        <!-- 底部提交按钮 -->
        <view class="bg-box">
            <view class="add-btn" @click="submitForm">提交</view>
        </view>

        <!-- 地区选择器 -->
        <address-picker @submitAddress="submitAddress" :pickerAreaShow="pickerAreaShow"
            @addressCanel="pickerAreaShow = false" :titleShow="false" />

        <!-- 养殖场性质选择器 -->
        <u-select confirm-color='#40CA8F' v-model="showNatureSelect" mode="single-column" :list="natureList"
            label-name="label" @confirm="selectNature" value-name="value" />
    </view>
</template>

<script>
import addressPicker from '@/components/address-picker-level3/index.vue'
import { pastureAdd } from '@/api/pages/livestock/farm'
import { getDicts } from "@/api/dict.js"
import CustomNavbar from '../components/CustomNavbar.vue'

export default {
    name: 'addFarm',

    components: {
        addressPicker,
        CustomNavbar
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            form: {
                pastureName: '',
                address: '',
                pastureNature: '',
                provinceName: '',
                provinceId: '',
                cityName: '',
                cityId: '',
                countyName: '',
                countyId: ''
            },
            pickerAreaShow: false,
            showNatureSelect: false,
            errorType: ['message'],
            customStyle: { textAlign: 'right', fontSize: '26rpx' },
            labelStyle: { color: '#333', fontSize: '26rpx' },
            placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
            isSubmitting: false,
            natureList: [],
            rules: {
                pastureName: [{
                    required: true,
                    message: '请输入养殖场名称',
                    trigger: ['blur', 'change']
                }],
                provinceName: [{
                    required: true,
                    message: '请选择养殖场位置',
                    trigger: ['blur', 'change']
                }],
                address: [{
                    required: true,
                    message: '请输入详细地址',
                    trigger: ['blur', 'change']
                }],
                pastureNature: [{
                    required: true,
                    message: '请选择养殖场性质',
                    trigger: ['blur', 'change']
                }]
            }
        }
    },

    computed: {
        navbarTotalHeight() {
            const statusBarHeight = this.systemInfo.statusBarHeight || 0;
            const navbarHeight = 64;
            return statusBarHeight + navbarHeight;
        },
        isFormValid() {
            const { pastureName, provinceName, address, pastureNature } = this.form
            return pastureName && provinceName && address && pastureNature !== ''
        }
    },

    onLoad() {
        this.loadNatureDict();
    },

    onReady() {
        this.$refs.uForm?.setRules?.(this.rules)
    },

    methods: {
        // 加载养殖场性质字典
        async loadNatureDict() {
            try {
                const res = await getDicts('pasture_nature');
                if (res && res.data) {
                    this.natureList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载字典失败:', error);
            }
        },

        // 获取养殖场性质文本
        getNatureText(value) {
            const nature = this.natureList.find(item => item.value === value)
            return nature ? nature.label : ''
        },

        submitAddress(val) {
            if (!val?.areaName || !val?.areaValue) return

            const areaName = val.areaName.split('-')
            const areaId = val.areaValue.split(',')

            this.form = {
                ...this.form,
                provinceName: areaName[0] || '',
                provinceId: areaId[0] || '',
                cityName: areaName[1] || '',
                cityId: areaId[1] || '',
                countyName: areaName[2] || '',
                countyId: areaId[2] || ''
            }

            this.pickerAreaShow = false
            this.resetField('provinceName')
        },

        // 选择养殖场性质
        selectNature(value) {
            if (!value?.length) return
            this.form.pastureNature = value[0].value
            this.showNatureSelect = false
            this.resetField('pastureNature')
        },

        // 提交表单
        async submitForm() {
            if (this.isSubmitting) return
            if (!this.isFormValid) {
                return this.$toast('请填写完整信息')
            }

            try {
                this.isSubmitting = true
                const valid = await this.validateForm()
                if (!valid) return

                const res = await pastureAdd(this.form)
                if (res.code === 200) {
                    uni.$emit('updateFarmList')
                    this.$toast('添加成功')
                    uni.navigateBack({ delta: 1 })
                } else {
                    throw new Error(res.message || '提交失败')
                }
            } catch (error) {
                this.handleError(error, '提交失败')
            } finally {
                this.isSubmitting = false
            }
        },

        validateForm() {
            return new Promise(resolve => {
                this.$refs.uForm.validate(valid => resolve(valid))
            })
        },

        handleError(error, customMessage = '') {
            console.error(error)
            this.$toast(error.message || customMessage || '操作失败')
        },

        resetField(value) {
            if (!value) return
            this.$refs.uForm?.fields?.forEach(field => {
                if (field.prop === value) {
                    field.resetField()
                }
            })
        }
    },
}
</script>

<style lang="less" scoped>
.main {
    height: calc(100vh - 126rpx);
    background-color: #F7F8F7;
}

.container {
    margin: 25rpx;
    background: #fff;
    box-sizing: border-box;
    padding: 30rpx 32rpx 40rpx;
    border-radius: 30rpx;

    /deep/ .u-form-item {
        padding: 20rpx 20rpx !important;
    }

    .tips {
        font-size: 28rpx;
        color: #999;
    }
}

.bg-box {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 126rpx;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;
    padding: 20rpx 30rpx;
    box-sizing: border-box;
}

.add-btn {
    width: 100%;
    height: 86rpx;
    background: linear-gradient(101deg, #19AF77 0%, #40CA8F 100%);
    border-radius: 50rpx;
    font-weight: 600;
    font-size: 34rpx;
    color: #FFFFFF;
    text-align: center;
    line-height: 86rpx;
}

.tips {
    font-size: 28rpx;
    color: #c0c3ca;
}

.common {
    font-size: 28rpx;
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    color: #333;
    margin: 10rpx;
    text-align: right;
}
</style>
