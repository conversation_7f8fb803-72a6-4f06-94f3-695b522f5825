<template>
    <view class="container">
        <CustomNavbar title="养殖场管理" :titleColor="'##333333'" />
        <view class="content" :style="{ paddingTop: navbarTotalHeight + 'px' }">
            <!-- 养殖场列表 -->
            <view class="farm-list">
                <view
                    v-for="item in farmList"
                    :key="item.pastureId"
                    class="farm-item"
                >
                    <!-- 牧场图标 -->
                    <image
                        class="farm-icon"
                        src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/muchang.png"
                        mode="aspectFit"
                    />

                    <!-- 牧场信息 -->
                    <view class="farm-info">
                        <view class="farm-header">
                            <text class="farm-name">{{ item.pastureName }}</text>
                            <view class="farm-nature">
                                <image
                                    class="nature-icon"
                                    src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/edit.png"
                                    mode="aspectFit"
                                />
                                <text class="nature-text">{{ item.pastureNatureName }}</text>
                            </view>
                        </view>
                        <text class="farm-address">{{ getFullAddress(item) }}</text>
                    </view>

                    <!-- 编辑按钮 -->
                    <view class="edit-btn" @click="editFarm(item)">
                        <image
                            class="edit-icon"
                            src="https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/cow/edit.png"
                            mode="aspectFit"
                        />
                    </view>
                </view>
            </view>

            <!-- 加载更多 -->
            <view v-if="loading" class="loading">
                <text>加载中...</text>
            </view>

            <!-- 没有更多数据 -->
            <view v-if="!hasMore && farmList.length > 0" class="no-more">
                <text>没有更多数据了</text>
            </view>

            <!-- 空状态 -->
            <view v-if="!loading && farmList.length === 0" class="empty">
                <text>暂无养殖场数据</text>
            </view>
        </view>

        <!-- 新增按钮 -->
        <view class="add-btn-container">
            <view class="add-btn" @click="addFarm">
                <text class="add-btn-text">新增养殖场</text>
            </view>
        </view>
    </view>
</template>

<script>

import CustomNavbar from '../components/CustomNavbar.vue'
import { pasturePage } from '@/api/pages/livestock/farm'

export default {
    components: {
        CustomNavbar,
    },
    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            farmList: [], // 养殖场列表
            loading: false, // 加载状态
            hasMore: true, // 是否还有更多数据
            pageNum: 1, // 当前页码
            pageSize: 20, // 每页数量
        }
    },
    onLoad() {
        this.loadFarmList();
    },
    onUnload() {
    },
    onShow() { },
    computed: {
        navbarTotalHeight() {
            const statusBarHeight = this.systemInfo.statusBarHeight || 0;
            const navbarHeight = 44;
            return statusBarHeight + navbarHeight;
        }
    },
    methods: {
        // 加载养殖场列表
        async loadFarmList(refresh = false) {
            if (this.loading) return;

            this.loading = true;

            try {
                if (refresh) {
                    this.pageNum = 1;
                    this.hasMore = true;
                }

                const params = {
                    pageNum: this.pageNum,
                    pageSize: this.pageSize
                };

                const response = await pasturePage(params);

                if (response && response.rows) {
                    if (refresh) {
                        this.farmList = response.rows;
                    } else {
                        this.farmList = [...this.farmList, ...response.rows];
                    }

                    // 判断是否还有更多数据
                    this.hasMore = response.rows.length === this.pageSize;

                    if (this.hasMore) {
                        this.pageNum++;
                    }
                } else {
                    this.hasMore = false;
                }
            } catch (error) {
                console.error('加载养殖场列表失败:', error);
                uni.showToast({
                    title: '加载失败',
                    icon: 'none'
                });
            } finally {
                this.loading = false;
            }
        },

        // 获取完整地址
        getFullAddress(item) {
            const parts = [
                item.provinceName,
                item.cityName,
                item.countyName,
                item.address
            ].filter(part => part && part.trim());

            return parts.join('');
        },

        // 编辑养殖场
        editFarm(item) {
            // TODO: 跳转到编辑页面
            console.log('编辑养殖场:', item);
            uni.showToast({
                title: '编辑功能待开发',
                icon: 'none'
            });
        },

        // 新增养殖场
        addFarm() {
            // TODO: 跳转到新增页面
            console.log('新增养殖场');
            uni.showToast({
                title: '新增功能待开发',
                icon: 'none'
            });
        },

        // 下拉刷新
        onRefresh() {
            this.loadFarmList(true);
        },

        // 上拉加载更多
        onLoadMore() {
            if (this.hasMore && !this.loading) {
                this.loadFarmList();
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
    background-color: #F7F8F7;
}

.content {
    padding: 30rpx;
    padding-bottom: 146rpx; // 为底部按钮留出空间
}

.farm-list {
    .farm-item {
        width: 690rpx;
        height: 202rpx;
        background-color: #FFFFFF;
        border-radius: 20rpx;
        margin-bottom: 30rpx;
        display: flex;
        align-items: center;
        padding: 0 30rpx;
        box-sizing: border-box;
        position: relative;

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.farm-icon {
    width: 80rpx;
    height: 80rpx;
    margin-right: 24rpx;
    flex-shrink: 0;
}

.farm-info {
    flex: 1;

    .farm-header {
        display: flex;
        align-items: center;
        margin-bottom: 16rpx;

        .farm-name {
            font-weight: bold;
            font-size: 34rpx;
            color: #333333;
            margin-right: 18rpx;
        }

        .farm-nature {
            display: flex;
            align-items: center;

            .nature-icon {
                width: 118rpx;
                height: 40rpx;
                margin-right: 8rpx;
            }

            .nature-text {
                font-size: 24rpx;
                color: #FF8C42;
            }
        }
    }

    .farm-address {
        font-weight: 400;
        font-size: 26rpx;
        color: #999999;
        line-height: 1.4;
    }
}

.edit-btn {
    position: absolute;
    right: 40rpx;
    top: 50%;
    transform: translateY(-50%);

    .edit-icon {
        width: 26rpx;
        height: 26rpx;
    }
}

// 加载状态和空状态
.loading, .no-more, .empty {
    text-align: center;
    padding: 40rpx 0;
    color: #999999;
    font-size: 28rpx;
}

// 新增按钮
.add-btn-container {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    width: 750rpx;
    height: 126rpx;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 100;

    .add-btn {
        width: 690rpx;
        height: 86rpx;
        background: linear-gradient(101deg, #19AF77 0%, #40CA8F 100%);
        border-radius: 50rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        .add-btn-text {
            font-family: PingFangSC, PingFang SC;
            font-weight: 600;
            font-size: 34rpx;
            color: #FFFFFF;
        }
    }
}
</style>
