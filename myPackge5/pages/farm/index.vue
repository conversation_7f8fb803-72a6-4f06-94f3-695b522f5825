<template>
    <view class="container">
        <CustomNavbar title="养殖场管理" :titleColor="'##333333'" />
        <view class="content" :style="{ paddingTop: navbarTotalHeight + 'px' }">

        </view>
    </view>
</template>

<script>

import CustomNavbar from '../components/CustomNavbar.vue'

export default {
    components: {
        CustomNavbar,
    },
    data() {
        return {
            isIphonex: getApp().globalData.systemInfo.isIphonex,
            systemInfo: uni.getSystemInfoSync(),
        }
    },
    onLoad() {

    },
    onUnload() {
    },
    onShow() { },
    computed: {
        navbarTotalHeight() {
            const statusBarHeight = this.systemInfo.statusBarHeight || 0;
            const navbarHeight = 44;
            return statusBarHeight + navbarHeight;
        }
    },
    methods: {}
}
</script>

<style lang="scss" scoped>
.container {
    min-height: 100vh;
}

.content {
    padding: 30rpx;
    background-color: #F7F8F7;
}
</style>
