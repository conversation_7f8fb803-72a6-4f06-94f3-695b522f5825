import request from '@/common/utils/ajax'
import { nmbService, xmbtest } from '../../base'

const API_PATHS = {
  LIST: 'pasture/manager/pageV2', // 养殖场分页列表
  ADD: 'pasture/addV2', // 新增养殖场
}
export function pasturePage(param) {
  return request.ajax(xmbtest + API_PATHS.LIST, param, 'POST').then((res) => res.data)
}

export function pastureAdd(param) {
  return request.ajax(xmbtest + API_PATHS.ADD, param, 'POST').then((res) => res.data)
}
